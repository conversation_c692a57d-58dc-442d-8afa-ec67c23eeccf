package com.mucheng.mucute.client.game.entity

object MobList {
    val mobTypes = setOf(
        "minecraft:armadillo",
        "minecraft:bat",
        "minecraft:bee",
        "minecraft:blaze",
        "minecraft:bogged",
        "minecraft:camel",
        "minecraft:cat",
        "minecraft:cave_spider",
        "minecraft:chicken",
        "minecraft:cod",
        "minecraft:cow",
        "minecraft:creeper",
        "minecraft:dolphin",
        "minecraft:donkey",
        "minecraft:drowned",
        "minecraft:elder_guardian",
        "minecraft:ender_dragon",
        "minecraft:enderman",
        "minecraft:endermite",
        "minecraft:evoker",
        "minecraft:fox",
        "minecraft:frog",
        "minecraft:ghast",
        "minecraft:glow_squid",
        "minecraft:goat",
        "minecraft:guardian",
        "minecraft:hoglin",
        "minecraft:horse",
        "minecraft:husk",
        "minecraft:illusioner",
        "minecraft:iron_golem",
        "minecraft:llama",
        "minecraft:magma_cube",
        "minecraft:mooshroom",
        "minecraft:mule",
        "minecraft:ocelot",
        "minecraft:panda",
        "minecraft:parrot",
        "minecraft:phantom",
        "minecraft:pig",
        "minecraft:piglin",
        "minecraft:piglin_brute",
        "minecraft:pillager",
        "minecraft:polar_bear",
        "minecraft:pufferfish",
        "minecraft:rabbit",
        "minecraft:ravager",
        "minecraft:salmon",
        "minecraft:sheep",
        "minecraft:shulker",
        "minecraft:silverfish",
        "minecraft:skeleton",
        "minecraft:skeleton_horse",
        "minecraft:slime",
        "minecraft:snow_golem",
        "minecraft:spider",
        "minecraft:squid",
        "minecraft:stray",
        "minecraft:strider",
        "minecraft:tadpole",
        "minecraft:trader_llama",
        "minecraft:tropical_fish",
        "minecraft:turtle",
        "minecraft:vex",
        "minecraft:villager",
        "minecraft:vindicator",
        "minecraft:wandering_trader",
        "minecraft:warden",
        "minecraft:witch",
        "minecraft:wither",
        "minecraft:wither_skeleton",
        "minecraft:wolf",
        "minecraft:zoglin",
        "minecraft:zombie",
        "minecraft:zombie_horse",
        "minecraft:zombie_villager",
        "minecraft:zombified_piglin",
        "minecraft:sniffer"
    )
}
