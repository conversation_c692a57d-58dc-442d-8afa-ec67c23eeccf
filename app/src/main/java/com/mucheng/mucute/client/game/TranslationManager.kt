package com.mucheng.mucute.client.game

object TranslationManager {

    private val map = HashMap<String, Map<String, String>>()

    init {
        map["en"] = en()
        map["zh"] = zh()
    }

    private fun en() = buildMap {
        put("fly", "Fly")
        put("no_clip", "No Clip")
        put("zoom", "Zoom")
        put("air_jump", "Air Jump")
        put("speed", "Speed")
        put("full_bright", "Full Bright")
        put("haste", "Haste")
        put("jetpack", "Jetpack")
        put("levitation", "Levitation")
        put("high_jump", "High Jump")
        put("slow_falling", "Slow Falling")
        put("anti_knockback", "Anti Knockback")
        put("poseidon", "Poseidon")
        put("regeneration", "Regen")
        put("bhop", "BHOP")
        put("sprint", "Sprint")
        put("no_hurt_camera", "No Hurt Cam")
        put("anti_afk", "Anti AFK")
        put("auto_walk", "Auto Walk")
        put("desync", "Desync")
        put("position_logger", "Position Logger")
        put("killaura", "KillAura")
        put("motion_fly", "Motion Fly")
        put("free_camera", "Free Camera")
        put("player_tracer", "Player Tracker")
        put("critic", "Criticals")
        put("nausea", "Nausea")
        put("health_boost", "Health Boost")
        put("jump_boost", "Jump Boost")
        put("resistance", "Resistance")
        put("fire_resist", "Fire Resistance")
        put("swiftness", "Swiftness")
        put("instant_health", "Instant Health")
        put("strength", "Strength")
        put("instant_damage", "Instant Damage")
        put("anti_crystal", "Anti Crystal")
        put("bad_omen", "Bad Omen")
        put("conduit_power", "Conduit Power")
        put("darkness", "Darkness")
        put("fatal_poison", "Fatal Poison")
        put("hunger", "Hunger")
        put("poison", "Poison")
        put("village_omen", "Village Hero")
        put("weakness", "Weakness")
        put("wither", "Wither")
        put("night_vision", "Night Vision")
        put("invisibility", "Invisibility")
        put("saturation", "Saturation")
        put("absorption", "Absorption")
        put("blindness", "Blindness")
        put("hunger", "Hunger")
        put("time_shift", "Time Changer")
        put("weather_controller", "Weather Control")
        put("crash", "Crash")
        put("fake_death", "FakeDeath")
        put("explosion_particle", "Explosion")
        put("bubble_particle", "Bubble")
        put("heart_particle", "Heart")
        put("xp_levels", "FakeXP")
        put("dust_particle", "Dust")
        put("fizz_particle", "Fizz")
        put("ender_eye_particle", "Ender Eye")
        put("breeze_block_explosion_particle", "Breeze")
        put("hitandrun", "Hit & Run")
        put("hitbox", "Hitbox")
        put("crystal_smash", "Crystal Smash")
        put("trigger_bot", "Trigger Bot")
        put("speed_display", "Speed Display")
        put("no_chat", "NoChat")
        put("network_info", "NetworkInfo")
        put("coordinates", "Coordinates")
        put("mining_fatigue", "Mining Fatigue")
        put("world_state", "World State")
        put("esp", "ESP")
        put("baritone", "Baritone")
        put("replay_mod", "ReplayMod")

        // Below for module options
        put("times", "Times")
        put("flySpeed", "Fly Speed")
        put("range", "Range")
        put("cps", "CPS")
        put("amplifier", "Amplifier")
        put("nightVision", "Night Vision")
        put("scanRadius", "Scan Radius")
        put("jumpHeight", "Jump Height")
        put("verticalUpSpeed", "Vertical Up Speed")
        put("verticalDownSpeed", "Vertical Down Speed")
        put("motionInterval", "Motion Interval")
        put("glideSpeed", "Glide Speed")
        put("vanillaFly", "Vanilla Fly")
        put("repeat", "Repeat")
        put("delay", "Delay")
        put("enabled", "Enabled")
        put("disabled", "Disabled")
        put("players_only", "Players Only")
        put("mobs_only", "Mob Aura")
        put("time", "Time")
        put("keep_distance", "Distance")
        put("tp_speed", "Teleport Speed")
        put("packets", "Packets")
        put("strafe", "Strafe")
        put("tp_aura", "TP Aura")
        put("teleport_behind", "TP Behind")
        put("strafe_angle", "Strafe Angle")
        put("strafe_speed", "Strafe Speed")
        put("strafe_radius", "Strafe Radius")
        put("clear", "Clear")
        put("rain", "Rain")
        put("thunderstorm", "Thunderstorm")
        put("intensity", "Intensity")
        put("interval", "Interval")
        put("walk_speed", "Walk Speed")
        put("fall_speed", "Fall Speed")
        put("speed", "Speed")
        put("count", "Count")
        put("random_offset", "Random Offset")
        put("offset_radius", "Offset Radius")
        put("height_offset", "Height Offset")
        put("levels", "Levels")
        put("threshold", "Threshold")
        put("hit_speed", "Hit Speed")
        put("jump_height", "Jump Height")
        put("circle_radius", "Circle Radius")
        put("include_mobs", "Include Mobs")
        put("track_all", "Track Everyone")
        put("closest_player", "Closest Player")
        put("track_mobs", "Track Mobs")
        put("hitbox_size", "Hitbox Size")
        put("particles", "Particles")
        put("visualize", "Visualize")
        put("size", "Size")
        put("ylevel", "Y Level")
        put("colored_text", "Colored Text")
        put("speed_smoothing", "Speed Smoothing")
        put("block_all", "Block All")
        put("block_player_chat", "Block Player Chat")
        put("block_system_chat", "Block System Chat")
        put("block_whispers", "Block Whispers")
        put("block_announcements", "Block Announcements")
        put("block_join_leave", "Block Join/Leave")
        put("height", "Height")
        put("width", "Width")
        put("decimal_places", "Decimal Places")
        put("show_direction", "Show Direction")
        put("show_packets", "Show Packets")
        put("show_entities", "Show Entities")
        put("show_players", "Show Players")
        put("show_time", "Show Time")
        put("show_chunks", "Show Chunks")
        put("colored_text", "Colored Text")
        put("update_interval", "Update Interval")
        put("fov", "FOV")
        put("stroke_width", "Stroke Width")
        put("color_red", "Red")
        put("color_green", "Green")
        put("color_blue", "Blue")
        put("show_all_entities", "Show All Entities")
        put("show_distance", "Show Distance")
        put("show_names", "NameTags")
        put("2d_box", "2D Box")
        put("3d_box", "3D Box")
        put("corner_box", "Corner Box")
        put("tracers", "Tracers")
        put("tracer_bottom", "Tracer from Bottom")
        put("tracer_top", "Tracer from Top")
        put("tracer_center", "Tracer from Center")
        put("interval", "Recording Interval")
        put("auto_save", "Auto Save")
        put("playback_speed", "Playback Speed")
        put("record_inputs", "Record Inputs")
        put("smooth", "Smooth Playback")
        put("completion_distance", "Completion Distance")
    }

    private fun zh() = buildMap {
        put("fly", "飞行")
        put("no_clip", "穿墙")
        put("zoom", "缩放")
        put("air_jump", "空中跳跃")
        put("speed", "速度")
        put("full_bright", "夜视")
        put("haste", "急速")
        put("jetpack", "喷气背包")
        put("levitation", "飘浮")
        put("high_jump", "高跳")
        put("slow_falling", "缓降")
        put("anti_knockback", "防击退")
        put("poseidon", "海神")
        put("regeneration", "生命恢复")
        put("bhop", "连跳")
        put("sprint", "疾跑")
        put("no_hurt_camera", "无伤害抖动")
        put("anti_afk", "防挂机")
        put("auto_walk", "自动行走")
        put("desync", "异步发包")
        put("position_logger", "实体追踪器")
        put("killaura", "杀戮光环")
        put("motion_fly", "动量飞行")
        put("free_camera", "自由视角")
        put("player_tracer", "玩家追踪器")
        put("critic", "扣字大神")
        put("nausea", "反胃")
        put("health_boost", "生命提升")
        put("jump_boost", "跳跃增强")
        put("resistance", "抗性")
        put("fire_resist", "抗火")
        put("swiftness", "速度")
        put("instant_health", "瞬间治疗")
        put("strength", "力量")
        put("instant_damage", "瞬间伤害")
        put("anti_crystal", "反水晶")
        put("bad_omen", "凶兆")
        put("conduit_power", "潮涌能量")
        put("darkness", "黑暗")
        put("fatal_poison", "剧毒")
        put("hunger", "饥饿")
        put("poison", "中毒")
        put("village_omen", "村庄英雄")
        put("weakness", "虚弱")
        put("wither", "凋零")
        put("night_vision", "夜视")
        put("invisibility", "隐身")
        put("saturation", "饱和")
        put("absorption", "伤害吸收")
        put("blindness", "失明")
        put("hunger", "饥饿")
        put("time_shift", "时间修改器")
        put("weather_controller", "天气控制器")
        put("crash", "崩溃")
        put("fake_death", "假死")
        put("network_info", "网络信息")
        put("mining_fatigue", "采矿疲劳")
        put("world_state", "世界状态")
        put("explosion_particle", "爆炸粒子")
        put("bubble_particle", "气泡粒子")
        put("heart_particle", "心形粒子")
        put("xp_levels", "经验")
        put("dust_particle", "尘埃粒子")
        put("fizz_particle", "气泡粒子")
        put("ender_eye_particle", "末影眼粒子")
        put("breeze_block_explosion_particle", "微风粒子")
        put("hitandrun", "打跑")
        put("hitbox", "碰撞箱")
        put("crystal_smash", "水晶光环")
        put("trigger_bot", "准心自动攻击")
        put("speed_display", "速度显示")
        put("no_chat", "关聊天")
        put("coordinates", "坐标")
        put("show_entities", "显示实体")
        put("show_players", "显示玩家")
        put("show_direction", "显示方向")
        put("show_packets", "显示数据包")
        put("show_time", "显示时间")
        put("show_chunks", "显示区块")
        put("update_interval", "更新间隔")

        // 模块选项
        put("times", "次数")
        put("flySpeed", "飞行速度")
        put("range", "范围")
        put("cps", "CPS")
        put("amplifier", "等级")
        put("nightVision", "夜视")
        put("scanRadius", "扫描半径")
        put("jumpHeight", "跳跃高度")
        put("verticalUpSpeed", "上升速度")
        put("verticalDownSpeed", "下降速度")
        put("motionInterval", "跳跃间隔")
        put("glideSpeed", "滑行速度")
        put("vanillaFly", "原版飞行")
        put("repeat", "重复")
        put("delay", "延迟")
        put("enabled", "启用")
        put("disabled", "禁用")
        put("players_only", "仅限玩家")
        put("mobs_only", "生物光环")
        put("time", "时间")
        put("keep_distance", "安全距离")
        put("tp_speed", "传送速度")
        put("packets", "数据包")
        put("strafe", "平移")
        put("tp_aura", "传送光环")
        put("teleport_behind", "传送到后方")
        put("strafe_angle", "平移角度")
        put("strafe_speed", "平移速度")
        put("strafe_radius", "平移半径")
        put("clear", "晴朗")
        put("rain", "下雨")
        put("thunderstorm", "雷暴")
        put("intensity", "强度")
        put("interval", "间隔")
        put("walk_speed", "行走速度")
        put("fall_speed", "下落速度")
        put("count", "数量")
        put("random_offset", "随机偏移")
        put("offset_radius", "偏移半径")
        put("height_offset", "高度偏移")
        put("levels", "等级")
        put("threshold", "击退阈值")
        put("hit_speed", "攻击速度")
        put("jump_height", "跳跃高度")
        put("circle_radius", "圆形半径")
        put("include_mobs", "包括怪物")
        put("track_all", "追踪所有")
        put("closest_player", "最近玩家")
        put("track_mobs", "追踪怪物")
        put("hitbox_size", "碰撞箱大小")
        put("particles", "粒子效果")
        put("visualize", "可视化")
        put("size", "尺寸")
        put("ylevel", " Y轴偏移量")
        put("colored_text", "彩色文本")
        put("speed_smoothing", "速度平滑")
        put("block_all", "屏蔽所有")
        put("block_player_chat", "屏蔽玩家聊天")
        put("block_system_chat", "屏蔽系统消息")
        put("block_whispers", "屏蔽私聊")
        put("block_announcements", "屏蔽公告")
        put("block_join_leave", "屏蔽进离消息")
        put("height", "高度")
        put("width", "宽度")
        put("decimal_places", "小数位")
    }

    fun getTranslationMap(language: String): Map<String, String> {
        val translationMap = map[language]
        if (translationMap != null) {
            return translationMap
        }

        map.forEach { (key, value) ->
            if (key.startsWith(language)) {
                return value
            }
        }

        return map["en"]!!
    }
}