package com.mucheng.mucute.client.game.data

class Effect(val id: Int, var amplifier: Int, var duration: Int) {

    companion object {
        const val SPEED = 1
        const val SLOWNESS = 2
        const val HASTE = 3
        const val SWIFTNESS = 3
        const val FATIGUE = 4
        const val MINING_FATIGUE = 4
        const val STRENGTH = 5
        const val HEALING = 6
        const val INSTANT_HEALTH = 6
        const val HARMING = 7
        const val INSTANT_DAMAGE = 7
        const val JUMP = 8
        const val JUMP_BOOST = 8
        const val NAUSEA = 9
        const val CONFUSION = 9
        const val REGENERATION = 10
        const val DAMAGE_RESISTANCE = 11
        const val RESISTANCE = 11
        const val FIRE_RESISTANCE = 12
        const val WATER_BREATHING = 13
        const val INVISIBILITY = 14
        const val BLINDNESS = 15
        const val NIGHT_VISION = 16
        const val HUNGER = 17
        const val WEAKNESS = 18
        const val POISON = 19
        const val WITHER = 20
        const val HEALTH_BOOST = 21
        const val ABSORPTION = 22
        const val SATURATION = 23
        const val LEVITATION = 24
        const val FATAL_POISON = 25
        const val CONDUIT_POWER = 26
        const val SLOW_FALLING = 27
        const val BAD_OMEN = 28
        const val VILLAGE_HERO = 29
        const val DARKNESS = 30
    }
}