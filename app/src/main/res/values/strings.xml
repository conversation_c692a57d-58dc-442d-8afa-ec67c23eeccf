<resources>
    <string name="app_name" translatable="false">MuCuteClient</string>
    <string name="home">Home</string>
    <string name="what_is_this">What\'s this?</string>
    <string name="introduction">MuCuteClient is a Minecraft Bedrock client that utilizes a MITM approach, providing various utilities to enhance the gameplay experience without modifying the game\'s memory.</string>
    <string name="backend" translatable="false">MuCuteRelay</string>
    <string name="backend_introduction">Set up a MITM-based interception in a few simple steps.</string>
    <string name="minecraft" translatable="false">Minecraft</string>
    <string name="recommended_version">Recommended version: %s</string>
    <string name="capturing_game_packets">Capturing game packets...</string>
    <string name="stop">Stop</string>
    <string name="overlay_permission_denied">Overlay permission denied</string>
    <string name="notification_permission_denied">Notification permission denied</string>
    <string name="request_overlay_permission">We need overlay permission to display the function floating window</string>
    <string name="game_settings">Game Settings</string>
    <string name="confirm">Confirm</string>
    <string name="cancel">Cancel</string>
    <string name="server_host_name">Server host name</string>
    <string name="server_port">Server port</string>
    <string name="tips">Tips</string>
    <string name="change_game_settings_tip">You need to disconnect before changing game settings.</string>
    <string name="backend_connected">MuCuteRelay connected.</string>
    <string name="start_game">Start Game</string>
    <string name="backend_disconnected">MuCuteRelay disconnected.</string>
    <string name="select_game">Select game</string>
    <string name="no_game_selected">No game selected</string>
    <string name="game_selector">Game Selector</string>
    <string name="select_game_first">Please select a game first.</string>
    <string name="failed_to_launch_game">Failed to launch the game.</string>
    <string name="account">Account</string>
    <string name="add_account">Add Account</string>
    <string name="fetch_account_failed">Fetch account failed: %s</string>
    <string name="delete">Delete</string>
    <string name="select">Select</string>
    <string name="unselect">Unselect</string>
    <string name="has_been_selected">(Selected)</string>
    <string name="server">Servers</string>
    <string name="about">About</string>
    <string name="settings">Settings</string>
    <string name="how_do_i_switch_login_mode">How do I switch login modes?</string>
    <string name="login_mode_introduction">If you want to log in to online mode, add an account and select it. If you want to log in to offline mode, deselect the account.</string>
    <string name="combat">Combat</string>
    <string name="motion">Motion</string>
    <string name="visual">Visual</string>
    <string name="particle">Particle</string>
    <string name="misc">Misc</string>
    <string name="config">Config</string>
    <string name="overlay_opacity">Overlay Button Opacity</string>
    <string name="shortcut_opacity">Shortcut Button Opacity</string>
    <string name="overlay_icon">Overlay Icon</string>
    <string name="overlay_icon_description">Change the floating button icon</string>
    <string name="overlay_opacity_settings">Opacity Settings</string>
    <string name="overlay_opacity_description">Adjust transparency of overlay and shortcut buttons</string>
    <string name="overlay_border_color">Overlay Border Color</string>
    <string name="overlay_border_color_description">Change the border color of the floating button</string>
    <string name="request_ignore_battery_optimization">We need to ignore battery optimization to stay connected</string>
    <string name="ignore_battery_optimization_denied">Ignore battery optimization permission denied</string>
    <string name="shortcut">Shortcut</string>
    <string name="check_updates_and_news">View Updates &amp; News</string>
    <string name="crash_happened">Crash Happened</string>
    <string name="cannot_back">A crash has occurred and you are not allowed to return. You can only restart the app.</string>
    <string name="effect">Effect</string>
    <string name="login_in">Log in using %s</string>
    <string name="fetch_account_successfully">Fetch account successfully</string>
    <string name="xbox_device_code">Xbox device code</string>
    <string name="refresh">Refresh</string>
</resources>